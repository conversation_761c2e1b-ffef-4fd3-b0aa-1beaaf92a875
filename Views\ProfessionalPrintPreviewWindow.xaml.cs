using System;
using System.Collections.Generic;
using System.Linq;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Windows.Markup;
using Microsoft.Win32;
using SFDSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ProfessionalPrintPreviewWindow : Window
    {
        private List<FrameworkElement> _pages;
        private int _currentPageIndex = 0;
        private double _currentZoom = 0.75; // 75% افتراضي
        private PrintQueue _selectedPrinter;
        private FixedDocument _printDocument;

        // أبعاد A4 بالبكسل (دقة فائقة 600 DPI للجودة الاحترافية)
        private const double A4_WIDTH_PIXELS = 4960;   // 8.27 inches × 600 DPI
        private const double A4_HEIGHT_PIXELS = 7016;  // 11.69 inches × 600 DPI
        
        // أبعاد A4 بـ DIP (96 DPI)
        private const double A4_WIDTH_DIP = 793.7;
        private const double A4_HEIGHT_DIP = 1122.52;

        public ProfessionalPrintPreviewWindow(List<FrameworkElement> pages)
        {
            try
            {
                InitializeComponent();

                _pages = pages ?? new List<FrameworkElement>();
                _currentZoom = 1.0;

                // إذا لم توجد صفحات، أنشئ صفحة تجريبية
                if (_pages.Count == 0)
                {
                    _pages.Add(CreateSamplePage());
                }

                // تأكد من تحميل العناصر أولاً
                this.Loaded += OnWindowLoaded_Safe;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء نافذة المعاينة: {ex.Message}");
            }
        }

        /// <summary>
        /// Constructor جديد لاستقبال FixedDocument
        /// </summary>
        public ProfessionalPrintPreviewWindow() : this(new List<FrameworkElement>())
        {
        }

        /// <summary>
        /// تعيين مستند الطباعة للمعاينة
        /// </summary>
        public void SetPrintDocument(FixedDocument document)
        {
            try
            {
                _printDocument = document;

                // استخراج الصفحات من المستند
                _pages = new List<FrameworkElement>();

                if (document != null && document.Pages.Count > 0)
                {
                    foreach (PageContent pageContent in document.Pages)
                    {
                        if (pageContent.Child is FixedPage fixedPage)
                        {
                            _pages.Add(fixedPage);
                        }
                    }
                }

                // إعادة تعيين الفهرس
                _currentPageIndex = 0;

                // تحديث المعاينة
                UpdatePreview();

                // تحديث شريط الحالة
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"تم تحميل {_pages?.Count ?? 0} صفحة للمعاينة";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعيين مستند الطباعة: {ex.Message}");

                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في تحميل المستند: {ex.Message}";
            }
        }

        private void OnWindowLoaded_Safe(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("بدء تحميل محتويات النافذة...", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                InitializeWindow();
                MessageBox.Show("تم تهيئة النافذة", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                LoadAvailablePrinters();
                MessageBox.Show("تم تحميل الطابعات", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                GeneratePrintDocument();
                MessageBox.Show("تم إنشاء مستند الطباعة", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                UpdatePreview();
                MessageBox.Show("تم تحديث المعاينة", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                UpdateThumbnails();
                MessageBox.Show("تم تحديث المصغرات", "تشخيص", MessageBoxButton.OK, MessageBoxImage.Information);

                MessageBox.Show("تم تحميل النافذة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل النافذة:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ مفصل", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeWindow()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء تهيئة النافذة...");

                // التأكد من وجود الصفحات
                if (_pages == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ _pages is null!");
                    _pages = new List<FrameworkElement>();
                }

                int pageCount = _pages.Count;
                System.Diagnostics.Debug.WriteLine($"📄 عدد الصفحات: {pageCount}");

                // تحديث معلومات الصفحات
                if (TotalPagesTextBlock != null)
                {
                    TotalPagesTextBlock.Text = $"من {pageCount}";
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديث TotalPagesTextBlock");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ TotalPagesTextBlock is null");
                }

                if (CurrentPageTextBox != null)
                {
                    CurrentPageTextBox.Text = "1";
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديث CurrentPageTextBox");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ CurrentPageTextBox is null");
                }

                if (PageInfoTextBlock != null)
                {
                    PageInfoTextBlock.Text = $"صفحة 1 من {pageCount}";
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديث PageInfoTextBlock");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ PageInfoTextBlock is null");
                }

                // تحديث مستوى التكبير
                if (ZoomLevelTextBlock != null)
                {
                    ZoomLevelTextBlock.Text = "75%";
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديث ZoomLevelTextBlock");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ ZoomLevelTextBlock is null");
                }

                // حالة جاهز
                if (StatusTextBlock != null)
                {
                    StatusTextBlock.Text = $"جاهز للطباعة - {pageCount} صفحة";
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديث StatusTextBlock");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ StatusTextBlock is null");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة النافذة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في InitializeWindow: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack Trace: {ex.StackTrace}");
            }
        }

        private void LoadAvailablePrinters()
        {
            try
            {
                if (PrinterComboBox != null)
                {
                    PrinterComboBox.Items.Clear();

                    // الحصول على جميع الطابعات المتاحة
                    var printServer = new LocalPrintServer();
                    var printQueues = printServer.GetPrintQueues();

                    foreach (var printer in printQueues)
                    {
                        var item = new ComboBoxItem
                        {
                            Content = printer.Name,
                            Tag = printer
                        };

                        PrinterComboBox.Items.Add(item);

                        // تحديد الطابعة الافتراضية (أول طابعة)
                        if (PrinterComboBox.SelectedItem == null)
                        {
                            PrinterComboBox.SelectedItem = item;
                            _selectedPrinter = printer;
                        }
                    }
                }

                    // إذا لم توجد طابعة افتراضية، اختر الأولى
                    if (PrinterComboBox.SelectedItem == null && PrinterComboBox.Items.Count > 0)
                    {
                        PrinterComboBox.SelectedIndex = 0;
                    }
                }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في تحميل الطابعات: {ex.Message}";

                // إضافة طابعة وهمية للاختبار
                if (PrinterComboBox != null)
                {
                    PrinterComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = "Microsoft Print to PDF",
                        Tag = null
                    });
                    PrinterComboBox.SelectedIndex = 0;
                }
            }
        }

        private void GeneratePrintDocument()
        {
            try
            {
                _printDocument = new FixedDocument();
                _printDocument.DocumentPaginator.PageSize = new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP);

                if (_pages != null)
                {
                    foreach (var page in _pages)
                    {
                        var fixedPage = CreatePrintablePage(page);
                        var pageContent = new PageContent();
                        pageContent.Child = fixedPage;
                        _printDocument.Pages.Add(pageContent);
                    }
                }

                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"تم إنشاء وثيقة الطباعة - {_printDocument.Pages.Count} صفحة";
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في إنشاء وثيقة الطباعة: {ex.Message}";

                System.Diagnostics.Debug.WriteLine($"خطأ في GeneratePrintDocument: {ex.Message}");
            }
        }

        private FixedPage CreatePrintablePage(FrameworkElement content)
        {
            var fixedPage = new FixedPage
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White
            };

            // إنشاء نسخة عالية الجودة من المحتوى - ملء الصفحة بالكامل
            var contentCopy = CreateHighQualityPageContent(content);

            // إضافة المحتوى بحيث يملأ الصفحة بالكامل
            var container = new Grid
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP
            };
            container.Children.Add(contentCopy);

            // إضافة رقم الصفحة في أسفل اليسار ومرفوع قليلاً
            var pageNumber = _pages?.IndexOf(content) + 1 ?? 1;
            var pageNumberContainer = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)), // خلفية شبه شفافة
                BorderBrush = new SolidColorBrush(Color.FromArgb(100, 128, 128, 128)), // حدود رمادية خفيفة
                BorderThickness = new Thickness(1, 1, 1, 1),
                CornerRadius = new CornerRadius(3),
                Padding = new Thickness(8, 4, 8, 4),
                Width = 35,
                Height = 20,
                HorizontalAlignment = HorizontalAlignment.Left, // تغيير إلى اليسار
                VerticalAlignment = VerticalAlignment.Bottom,
                Margin = new Thickness(5, 0, 0, 25), // يسار 5، أسفل 25 لرفعه قليلاً
                Child = new TextBlock
                {
                    Text = pageNumber.ToString(), // رقم الصفحة فقط بشكل احترافي
                    FontSize = 11,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(64, 64, 64)), // رمادي داكن
                    TextAlignment = TextAlignment.Center,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                }
            };

            container.Children.Add(pageNumberContainer);
            fixedPage.Children.Add(container);

            return fixedPage;
        }

        private FrameworkElement CreateHighQualityPageContent(FrameworkElement original)
        {
            try
            {
                // التأكد من تحديث التخطيط
                original.UpdateLayout();

                // إنشاء صورة عالية الجودة - دقة محسنة وملء الصفحة بالكامل
                var renderBitmap = new RenderTargetBitmap(
                    (int)(A4_WIDTH_PIXELS * 3), (int)(A4_HEIGHT_PIXELS * 3), // زيادة الدقة 300%
                    900, 900, PixelFormats.Pbgra32); // دقة فائقة 900 DPI

                // رسم المحتوى - ملء الصفحة بالكامل
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    var visualBrush = new VisualBrush(original)
                    {
                        Stretch = Stretch.Fill // ملء الصفحة بالكامل بدلاً من Uniform
                    };

                    drawingContext.DrawRectangle(
                        visualBrush, null,
                        new Rect(0, 0, A4_WIDTH_PIXELS, A4_HEIGHT_PIXELS));
                }

                renderBitmap.Render(drawingVisual);

                // إنشاء Image للعرض - ملء الصفحة بالكامل
                return new Image
                {
                    Source = renderBitmap,
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Stretch = Stretch.Fill // ملء الصفحة بالكامل
                };
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، إرجاع محتوى نصي
                return new TextBlock
                {
                    Text = $"خطأ في عرض المحتوى: {ex.Message}",
                    FontSize = 14,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(20)
                };
            }
        }

        private void UpdatePreview()
        {
            try
            {
                if (MainPreviewPanel != null)
                {
                    MainPreviewPanel.Children.Clear();

                    // استخدام _pages أولاً لأنها تحتوي على المحتوى الأصلي
                    if (_pages != null && _pages.Count > 0 && _currentPageIndex < _pages.Count)
                    {
                        // عرض الصفحة الحالية من الصفحات الأصلية
                        var currentPage = _pages[_currentPageIndex];
                        var pageElement = CreateDirectPreviewElement(currentPage);
                        MainPreviewPanel.Children.Add(pageElement);
                    }
                    else if (_printDocument != null && _printDocument.Pages.Count > 0 && _currentPageIndex < _printDocument.Pages.Count)
                    {
                        // عرض الصفحة الحالية من المستند المطبوع
                        var currentPage = _printDocument.Pages[_currentPageIndex];
                        var pageElement = CreatePreviewPageElement(currentPage.Child);
                        MainPreviewPanel.Children.Add(pageElement);
                    }
                    else
                    {
                        // عرض صفحة فارغة
                        var emptyPage = CreateSamplePage();
                        MainPreviewPanel.Children.Add(emptyPage);
                    }

                    // تطبيق مستوى التكبير
                    var transform = new ScaleTransform(_currentZoom, _currentZoom);
                    MainPreviewPanel.RenderTransform = transform;
                }

                // تحديث معلومات الصفحة
                if (CurrentPageTextBox != null)
                    CurrentPageTextBox.Text = (_currentPageIndex + 1).ToString();

                if (PageInfoTextBlock != null)
                    PageInfoTextBlock.Text = $"صفحة {_currentPageIndex + 1} من {_pages?.Count ?? 0}";

                // تحديث شريط الحالة مع معلومات مفصلة
                if (StatusTextBlock != null)
                {
                    var pageType = GetCurrentPageType();
                    StatusTextBlock.Text = $"جاهز للطباعة - {pageType}";
                }
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في تحديث المعاينة: {ex.Message}";

                System.Diagnostics.Debug.WriteLine($"خطأ في UpdatePreview: {ex.Message}");
            }
        }

        private Border CreatePreviewPageElement(FixedPage page)
        {
            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(10),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 315,
                    ShadowDepth = 5,
                    Opacity = 0.3
                }
            };

            // إنشاء نسخة من محتوى الصفحة
            var pageContent = new Canvas
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White
            };

            // نسخ محتوى الصفحة
            foreach (UIElement child in page.Children)
            {
                if (child is FrameworkElement element)
                {
                    var elementCopy = CreateElementCopy(element);
                    pageContent.Children.Add(elementCopy);
                }
            }

            border.Child = pageContent;
            return border;
        }

        private FrameworkElement CreateElementCopy(FrameworkElement original)
        {
            try
            {
                original.UpdateLayout();
                
                var renderBitmap = new RenderTargetBitmap(
                    (int)Math.Max(original.ActualWidth, 100),
                    (int)Math.Max(original.ActualHeight, 100),
                    96, 96, PixelFormats.Pbgra32);

                renderBitmap.Render(original);

                return new Image
                {
                    Source = renderBitmap,
                    Width = original.ActualWidth,
                    Height = original.ActualHeight,
                    HorizontalAlignment = original.HorizontalAlignment,
                    VerticalAlignment = original.VerticalAlignment,
                    Margin = original.Margin
                };
            }
            catch
            {
                return new Rectangle
                {
                    Fill = Brushes.LightGray,
                    Width = 100,
                    Height = 50
                };
            }
        }

        private void UpdateThumbnails()
        {
            try
            {
                if (ThumbnailsPanel != null)
                {
                    ThumbnailsPanel.Children.Clear();

                    for (int i = 0; i < _pages.Count; i++)
                    {
                        var thumbnail = CreateThumbnail(i);
                        ThumbnailsPanel.Children.Add(thumbnail);
                    }
                }
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في إنشاء المصغرات: {ex.Message}";

                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateThumbnails: {ex.Message}");
            }
        }

        private Border CreateThumbnail(int pageIndex)
        {
            var thumbnailBorder = new Border
            {
                Width = 150,
                Height = 200,
                Margin = new Thickness(5),
                BorderThickness = new Thickness(pageIndex == _currentPageIndex ? 3 : 1),
                BorderBrush = pageIndex == _currentPageIndex ? Brushes.Blue : Brushes.Gray,
                Background = Brushes.White,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // محتوى مصغر
            var thumbnailContent = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // صورة مصغرة
            var thumbnailImage = new Rectangle
            {
                Width = 120,
                Height = 160,
                Fill = Brushes.WhiteSmoke,
                Stroke = Brushes.LightGray,
                StrokeThickness = 1
            };

            // رقم الصفحة
            var pageNumber = new TextBlock
            {
                Text = (pageIndex + 1).ToString(),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0),
                FontSize = 10
            };

            thumbnailContent.Children.Add(thumbnailImage);
            thumbnailContent.Children.Add(pageNumber);
            thumbnailBorder.Child = thumbnailContent;

            // حدث النقر للانتقال للصفحة
            thumbnailBorder.MouseLeftButtonUp += (s, e) =>
            {
                _currentPageIndex = pageIndex;
                UpdatePreview();
                UpdateThumbnails();
            };

            return thumbnailBorder;
        }

        // أحداث الأزرار والتحكم
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🎨 بدء الطباعة الفيكتورية عالية الجودة...");

                if (_selectedPrinter != null && _pages?.Any() == true)
                {
                    var printDialog = new PrintDialog();
                    printDialog.PrintQueue = _selectedPrinter;

                    if (printDialog.ShowDialog() == true)
                    {
                        // إنشاء مستند فيكتوري عالي الجودة
                        var vectorDocument = VectorPrintService.CreateVectorDocument(_pages);

                        if (vectorDocument != null)
                        {
                            printDialog.PrintDocument(vectorDocument.DocumentPaginator, "تقرير الزيارة الميدانية - جودة فيكتورية");

                            if (StatusTextBlock != null)
                                StatusTextBlock.Text = "تم إرسال المستند الفيكتوري للطباعة";

                            System.Diagnostics.Debug.WriteLine("✅ تم إرسال المستند الفيكتوري للطباعة بنجاح");
                            MessageBox.Show("تم إرسال التقرير للطباعة بجودة فيكتورية عالية مثل الوورد", "نجح",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار طابعة صالحة والتأكد من وجود صفحات للطباعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة الفيكتورية: {ex.Message}");
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في الطباعة الفيكتورية: {ex.Message}";
                MessageBox.Show($"خطأ في الطباعة الفيكتورية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (_selectedPrinter != null)
                    printDialog.PrintQueue = _selectedPrinter;
                    
                printDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"خطأ في إعدادات الطباعة: {ex.Message}";
            }
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentZoom < 2.0)
                {
                    _currentZoom += 0.25;
                    UpdateZoom();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ZoomIn: {ex.Message}");
            }
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentZoom > 0.25)
                {
                    _currentZoom -= 0.25;
                    UpdateZoom();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ZoomOut: {ex.Message}");
            }
        }

        private void UpdateZoom()
        {
            try
            {
                // تطبيق التكبير على المحتوى
                if (MainPreviewPanel != null)
                {
                    var transform = new ScaleTransform(_currentZoom, _currentZoom);
                    MainPreviewPanel.RenderTransform = transform;
                }

                if (ZoomLevelTextBlock != null)
                {
                    ZoomLevelTextBlock.Text = $"{(_currentZoom * 100):F0}%";
                }

                // تحديث ComboBox
                if (ZoomComboBox != null && ZoomComboBox.Items != null)
                {
                    var zoomText = $"{(_currentZoom * 100):F0}%";
                    foreach (ComboBoxItem item in ZoomComboBox.Items)
                    {
                        if (item?.Content?.ToString() == zoomText)
                        {
                            ZoomComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في UpdateZoom: {ex.Message}");
            }
        }

        private void ZoomComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (ZoomComboBox?.SelectedItem is ComboBoxItem selectedItem && selectedItem.Content != null)
                {
                    var content = selectedItem.Content.ToString();

                    if (!string.IsNullOrEmpty(content))
                    {
                        if (content.EndsWith("%"))
                        {
                            if (double.TryParse(content.Replace("%", ""), out double zoomPercent))
                            {
                                _currentZoom = zoomPercent / 100.0;
                                UpdateZoom();
                            }
                        }
                        else if (content == "ملء الصفحة")
                        {
                            _currentZoom = 0.75; // تقريبي
                            UpdateZoom();
                        }
                        else if (content == "ملء العرض")
                        {
                            _currentZoom = 1.0; // تقريبي
                            UpdateZoom();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ZoomComboBox_SelectionChanged: {ex.Message}");
            }
        }

        private void FirstPageButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPageIndex = 0;
            UpdatePreview();
            UpdateThumbnails();
        }

        private void PreviousPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPageIndex > 0)
            {
                _currentPageIndex--;
                UpdatePreview();
                UpdateThumbnails();
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPageIndex < _pages.Count - 1)
            {
                _currentPageIndex++;
                UpdatePreview();
                UpdateThumbnails();
            }
        }

        private void LastPageButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPageIndex = _pages.Count - 1;
            UpdatePreview();
            UpdateThumbnails();
        }

        private void CurrentPageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (CurrentPageTextBox != null && int.TryParse(CurrentPageTextBox.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= _pages.Count)
                    {
                        _currentPageIndex = pageNumber - 1;
                        UpdatePreview();
                        UpdateThumbnails();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في CurrentPageTextBox_TextChanged: {ex.Message}");
            }
        }

        private void SinglePageView_Click(object sender, RoutedEventArgs e)
        {
            // تطبيق عرض صفحة واحدة
            UpdatePreview();
        }

        private void TwoPageView_Click(object sender, RoutedEventArgs e)
        {
            // تطبيق عرض صفحتين (سيتم تطبيقه لاحقاً)
            MessageBox.Show("عرض صفحتين سيتم تطبيقه قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void MultiPageView_Click(object sender, RoutedEventArgs e)
        {
            // تطبيق عرض عدة صفحات (سيتم تطبيقه لاحقاً)
            MessageBox.Show("عرض عدة صفحات سيتم تطبيقه قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrinterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (PrinterComboBox?.SelectedItem is ComboBoxItem selectedItem)
                {
                    _selectedPrinter = selectedItem.Tag as PrintQueue;
                    if (StatusTextBlock != null)
                        StatusTextBlock.Text = $"تم اختيار الطابعة: {selectedItem.Content}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في PrinterComboBox_SelectionChanged: {ex.Message}");
            }
        }

        private void PaperSizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تطبيق تغيير حجم الورق
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "تم تغيير حجم الورق";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في PaperSizeComboBox_SelectionChanged: {ex.Message}");
            }
        }

        private void OrientationComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تطبيق تغيير اتجاه الصفحة
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "تم تغيير اتجاه الصفحة";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في OrientationComboBox_SelectionChanged: {ex.Message}");
            }
        }

        private void PageRangeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (PageRangeComboBox?.SelectedItem is ComboBoxItem selectedItem && CustomRangeTextBox != null)
                {
                    CustomRangeTextBox.IsEnabled = selectedItem.Content?.ToString() == "نطاق مخصص";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في PageRangeComboBox_SelectionChanged: {ex.Message}");
            }
        }

        private void RefreshPrintersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadAvailablePrinters();
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "تم تحديث قائمة الطابعات";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في RefreshPrintersButton_Click: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private FrameworkElement CreateSamplePage()
        {
            var border = new Border
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(10)
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(new TextBlock
            {
                Text = "معاينة الطباعة",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            });

            stackPanel.Children.Add(new TextBlock
            {
                Text = "لا يوجد محتوى للعرض حالياً",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            });

            stackPanel.Children.Add(new TextBlock
            {
                Text = "يرجى التأكد من وجود تقرير للمعاينة",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center
            });

            border.Child = stackPanel;
            return border;
        }

        private Border CreateDirectPreviewElement(FrameworkElement element)
        {
            try
            {
                var border = new Border
                {
                    Background = Brushes.White,
                    BorderBrush = Brushes.LightGray,
                    BorderThickness = new Thickness(1),
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Margin = new Thickness(20),
                    Effect = new System.Windows.Media.Effects.DropShadowEffect
                    {
                        Color = Colors.Gray,
                        Direction = 315,
                        ShadowDepth = 5,
                        Opacity = 0.3
                    }
                };

                // إنشاء نسخة من العنصر للمعاينة
                FrameworkElement previewElement = null;

                if (element is UserControl userControl)
                {
                    // إنشاء نسخة جديدة من UserControl مع نفس DataContext
                    var newUserControl = new Views.ReportView();
                    newUserControl.DataContext = userControl.DataContext;

                    // تطبيق خصائص A4 مع دقة عالية
                    newUserControl.Width = A4_WIDTH_DIP;
                    newUserControl.Height = A4_HEIGHT_DIP;
                    newUserControl.FlowDirection = FlowDirection.RightToLeft;

                    // تحسين الدقة لمنع الضبابية
                    RenderOptions.SetBitmapScalingMode(newUserControl, BitmapScalingMode.HighQuality);
                    RenderOptions.SetEdgeMode(newUserControl, EdgeMode.Aliased);
                    TextOptions.SetTextFormattingMode(newUserControl, TextFormattingMode.Display);
                    TextOptions.SetTextRenderingMode(newUserControl, TextRenderingMode.ClearType);

                    // إجبار التحديث مع قياسات دقيقة
                    newUserControl.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                    newUserControl.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                    newUserControl.UpdateLayout();

                    previewElement = newUserControl;
                }
                else
                {
                    // للعناصر الأخرى، استخدم العنصر مباشرة
                    previewElement = element;
                    if (previewElement != null)
                    {
                        previewElement.Width = A4_WIDTH_DIP;
                        previewElement.Height = A4_HEIGHT_DIP;

                        // تحسين الدقة
                        RenderOptions.SetBitmapScalingMode(previewElement, BitmapScalingMode.HighQuality);
                        RenderOptions.SetEdgeMode(previewElement, EdgeMode.Aliased);
                    }
                }

                // استخدام Viewbox بدلاً من ScrollViewer لتحسين العرض
                var viewbox = new Viewbox
                {
                    Stretch = Stretch.Uniform,
                    StretchDirection = StretchDirection.DownOnly,
                    Child = previewElement
                };

                // تحسين دقة Viewbox
                RenderOptions.SetBitmapScalingMode(viewbox, BitmapScalingMode.HighQuality);

                border.Child = viewbox;
                return border;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في CreateDirectPreviewElement: {ex.Message}");

                // إرجاع عنصر بديل في حالة الخطأ
                var errorBorder = new Border
                {
                    Background = Brushes.White,
                    BorderBrush = Brushes.Red,
                    BorderThickness = new Thickness(2),
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Margin = new Thickness(20)
                };

                var errorText = new TextBlock
                {
                    Text = $"خطأ في عرض الصفحة: {ex.Message}",
                    FontSize = 14,
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    TextWrapping = TextWrapping.Wrap
                };

                errorBorder.Child = errorText;
                return errorBorder;
            }
        }

        /// <summary>
        /// تحديد نوع الصفحة الحالية
        /// </summary>
        private string GetCurrentPageType()
        {
            try
            {
                if (_pages == null || _pages.Count == 0)
                    return "صفحة فارغة";

                var totalPages = _pages.Count;
                var currentPage = _currentPageIndex + 1;

                if (currentPage == 1)
                    return "محضر استخراج عروض الأسعار";
                else if (currentPage <= totalPages)
                    return $"صفحة العقد {currentPage - 1}";
                else
                    return "صفحة غير معروفة";
            }
            catch
            {
                return "صفحة";
            }
        }
    }
}
