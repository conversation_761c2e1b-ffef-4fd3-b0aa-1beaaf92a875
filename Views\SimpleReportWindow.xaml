<Window x:Class="DriverManagementSystem.Views.SimpleReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير الزيارة الميدانية - نسخة مبسطة" 
        Height="600" Width="800"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E7D32" Padding="20">
            <TextBlock Text="📄 تقرير الزيارة الميدانية" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <Border Background="White" CornerRadius="10" Padding="30">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.3"/>
                </Border.Effect>
                
                <StackPanel>
                    <!-- Visit Info -->
                    <TextBlock Text="معلومات الزيارة" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                    
                    <Grid Margin="0,0,0,30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="رقم الزيارة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock x:Name="VisitNumberText" Text="{Binding VisitNumber}" FontSize="16" Background="#F0F0F0" Padding="10"/>
                            
                            <TextBlock Text="تاريخ الزيارة:" FontWeight="Bold" Margin="0,20,0,5"/>
                            <TextBlock x:Name="VisitDateText" Text="{Binding DepartureDate, StringFormat=dd/MM/yyyy}" FontSize="16" Background="#F0F0F0" Padding="10"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="غرض الزيارة:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock x:Name="PurposeText" Text="{Binding MissionPurpose}" FontSize="16" Background="#F0F0F0" Padding="10" TextWrapping="Wrap"/>
                            
                            <TextBlock Text="عدد الأيام:" FontWeight="Bold" Margin="0,20,0,5"/>
                            <TextBlock x:Name="DaysCountText" Text="{Binding DaysCount}" FontSize="16" Background="#F0F0F0" Padding="10"/>
                        </StackPanel>
                    </Grid>

                    <!-- Projects -->
                    <TextBlock Text="المشاريع" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                    <DataGrid x:Name="ProjectsGrid" 
                              ItemsSource="{Binding Projects}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Height="200"
                              Margin="0,0,0,30">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم المشروع" Binding="{Binding ProjectNumber}" Width="150"/>
                            <DataGridTextColumn Header="اسم المشروع" Binding="{Binding ProjectName}" Width="*"/>
                            <DataGridTextColumn Header="الموقع" Binding="{Binding Location}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Success Message -->
                    <Border Background="#E8F5E8" BorderBrush="#4CAF50" BorderThickness="2" 
                            CornerRadius="5" Padding="20" Margin="0,20,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="✅" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="تم تحميل بيانات التقرير بنجاح!" 
                                       FontSize="18" FontWeight="Bold" 
                                       Foreground="#2E7D32" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#E0E0E0" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🖨️ طباعة" 
                        Click="PrintButton_Click"
                        Background="#2E7D32" Foreground="White" 
                        Padding="20,10" Margin="0,0,20,0"
                        FontSize="16" FontWeight="Bold"/>
                
                <Button Content="📄 تصدير PDF" 
                        Click="ExportButton_Click"
                        Background="#1976D2" Foreground="White" 
                        Padding="20,10" Margin="0,0,20,0"
                        FontSize="16" FontWeight="Bold"/>
                
                <Button Content="❌ إغلاق" 
                        Click="CloseButton_Click"
                        Background="#D32F2F" Foreground="White" 
                        Padding="20,10"
                        FontSize="16" FontWeight="Bold"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
