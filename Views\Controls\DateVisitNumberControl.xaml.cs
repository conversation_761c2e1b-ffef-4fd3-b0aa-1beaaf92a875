using System.Windows;
using System.Windows.Controls;

namespace DriverManagementSystem.Views.Controls
{
    /// <summary>
    /// التحكم الموحد لعرض التاريخ ورقم الزيارة
    /// </summary>
    public partial class DateVisitNumberControl : UserControl
    {
        public DateVisitNumberControl()
        {
            InitializeComponent();
        }

        // خاصية التاريخ
        public static readonly DependencyProperty ReportDateProperty =
            DependencyProperty.Register("ReportDate", typeof(string), typeof(DateVisitNumberControl), 
                new PropertyMetadata("24/06/2025"));

        public string ReportDate
        {
            get { return (string)GetValue(ReportDateProperty); }
            set { SetValue(ReportDateProperty, value); }
        }

        // خاصية رقم الزيارة
        public static readonly DependencyProperty VisitNumberProperty =
            DependencyProperty.Register("VisitNumber", typeof(string), typeof(DateVisitNumberControl), 
                new PropertyMetadata("3333"));

        public string VisitNumber
        {
            get { return (string)GetValue(VisitNumberProperty); }
            set { SetValue(VisitNumberProperty, value); }
        }
    }
}
