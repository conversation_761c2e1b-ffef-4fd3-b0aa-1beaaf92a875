using System;
using System.Windows;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة تقرير مبسطة لتجنب مشاكل التعليق
    /// </summary>
    public partial class SimpleReportWindow : Window
    {
        private FieldVisit _selectedVisit;

        public SimpleReportWindow()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 إنشاء SimpleReportWindow بدون بيانات");
                InitializeComponent();
                ShowSampleData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء SimpleReportWindow: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء النافذة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public SimpleReportWindow(FieldVisit selectedVisit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 إنشاء SimpleReportWindow للزيارة: {selectedVisit?.VisitNumber ?? "غير محدد"}");
                
                InitializeComponent();
                _selectedVisit = selectedVisit;
                
                if (selectedVisit != null)
                {
                    LoadVisitData(selectedVisit);
                }
                else
                {
                    ShowSampleData();
                }
                
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء SimpleReportWindow بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء SimpleReportWindow: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء النافذة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadVisitData(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 تحميل بيانات الزيارة: {visit.VisitNumber}");
                
                // تعيين البيانات مباشرة للعناصر
                VisitNumberText.Text = visit.VisitNumber ?? "غير محدد";
                VisitDateText.Text = visit.DepartureDate.ToString("dd/MM/yyyy");
                PurposeText.Text = visit.MissionPurpose ?? "غير محدد";
                DaysCountText.Text = visit.DaysCount.ToString();
                
                // تحميل المشاريع
                if (visit.Projects?.Count > 0)
                {
                    ProjectsGrid.ItemsSource = visit.Projects;
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {visit.Projects.Count} مشروع");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع للزيارة");
                    ProjectsGrid.ItemsSource = null;
                }
                
                System.Diagnostics.Debug.WriteLine("✅ تم تحميل بيانات الزيارة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الزيارة: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowSampleData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 عرض بيانات تجريبية");
                
                VisitNumberText.Text = "تجريبي-001";
                VisitDateText.Text = DateTime.Now.ToString("dd/MM/yyyy");
                PurposeText.Text = "زيارة تجريبية لاختبار النظام";
                DaysCountText.Text = "1";
                
                ProjectsGrid.ItemsSource = null;
                
                System.Diagnostics.Debug.WriteLine("✅ تم عرض البيانات التجريبية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض البيانات التجريبية: {ex.Message}");
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ محاولة الطباعة");
                MessageBox.Show("سيتم تطبيق الطباعة قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 محاولة تصدير PDF");
                MessageBox.Show("سيتم تطبيق تصدير PDF قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تصدير PDF: {ex.Message}");
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("❌ إغلاق النافذة");
                this.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
            }
        }
    }
}
