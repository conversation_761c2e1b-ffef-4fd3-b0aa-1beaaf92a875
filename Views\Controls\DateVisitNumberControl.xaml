<UserControl x:Class="DriverManagementSystem.Views.Controls.DateVisitNumberControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="200">
    
    <!-- التصميم الموحد للتاريخ ورقم الزيارة - نفس المقاس الأصلي -->
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

        <!-- التاريخ -->
        <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1"
                Padding="8,4" Margin="0,0,0,4">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- أيقونة التاريخ -->
                <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                <!-- نص التاريخ -->
                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                    <Run Text="التاريخ: "/>
                    <Run Text="{Binding ReportDate, FallbackValue=24/06/2025}"/>
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- رقم الزيارة -->
        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1"
                Padding="8,4">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- أيقونة رقم الزيارة -->
                <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                <!-- نص رقم الزيارة -->
                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                    <Run Text="رقم الزيارة: "/>
                    <Run Text="{Binding VisitNumber, FallbackValue=3333}"/>
                </TextBlock>
            </StackPanel>
        </Border>

    </StackPanel>
</UserControl>
