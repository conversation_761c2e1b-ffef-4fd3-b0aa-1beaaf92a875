<UserControl x:Class="DriverManagementSystem.Views.Controls.DateVisitNumberControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="200">
    
    <!-- التصميم الموحد للتاريخ ورقم الزيارة -->
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

        <!-- التاريخ -->
        <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1"
                Padding="12,8" Margin="0,0,0,6" CornerRadius="4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="24"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- أيقونة التاريخ متساوية -->
                <Border Grid.Column="0" Background="#4682B4" Width="22" Height="22" CornerRadius="3"
                        HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="📅" FontSize="11" Foreground="White"
                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- نص التاريخ -->
                <TextBlock Grid.Column="1" FontSize="12" FontWeight="Bold" Foreground="#333333"
                           VerticalAlignment="Center" Margin="10,0,0,0">
                    <Run Text="التاريخ: "/>
                    <Run Text="{Binding ReportDate, FallbackValue=24/06/2025}"/>
                </TextBlock>
            </Grid>
        </Border>

        <!-- رقم الزيارة -->
        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1"
                Padding="12,8" CornerRadius="4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="24"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- أيقونة رقم الزيارة متساوية -->
                <Border Grid.Column="0" Background="#228B22" Width="22" Height="22" CornerRadius="3"
                        HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🔢" FontSize="11" Foreground="White"
                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- نص رقم الزيارة -->
                <TextBlock Grid.Column="1" FontSize="12" FontWeight="Bold" Foreground="#333333"
                           VerticalAlignment="Center" Margin="10,0,0,0">
                    <Run Text="رقم الزيارة: "/>
                    <Run Text="{Binding VisitNumber, FallbackValue=3333}"/>
                </TextBlock>
            </Grid>
        </Border>

    </StackPanel>
</UserControl>
