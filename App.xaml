<Application x:Class="DriverManagementSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem"
             xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
             xmlns:views="clr-namespace:DriverManagementSystem.Views"
             Startup="Application_Startup">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Colors.xaml"/>
                <ResourceDictionary Source="Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Value Converters -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:BooleanToIconConverter x:Key="BooleanToIconConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <converters:LoadingTextConverter x:Key="LoadingTextConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <views:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

        </ResourceDictionary>
    </Application.Resources>
</Application>
