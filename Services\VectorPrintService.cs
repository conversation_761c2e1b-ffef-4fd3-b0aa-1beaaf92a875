using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Windows.Markup;
using System.IO;
using System.Xml;

namespace SFDSystem.Services
{
    /// <summary>
    /// خدمة طباعة فيكتورية عالية الجودة مثل Microsoft Word
    /// تستخدم Vector Graphics بدلاً من Bitmap للحصول على جودة لا نهائية
    /// </summary>
    public class VectorPrintService
    {
        // أبعاد A4 بوحدة DIP (Device Independent Pixels)
        private const double A4_WIDTH_DIP = 793.7;   // 21.0 cm
        private const double A4_HEIGHT_DIP = 1122.52; // 29.7 cm
        private const double MARGIN_DIP = 20;         // هوامش صغيرة

        /// <summary>
        /// إنشاء مستند طباعة فيكتوري عالي الجودة
        /// </summary>
        public static FixedDocument CreateVectorDocument(List<FrameworkElement> pages)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🎨 إنشاء مستند فيكتوري عالي الجودة...");
                
                var fixedDocument = new FixedDocument();
                
                for (int i = 0; i < pages.Count; i++)
                {
                    var page = pages[i];
                    System.Diagnostics.Debug.WriteLine($"🖼️ معالجة الصفحة الفيكتورية {i + 1}");

                    // إنشاء صفحة فيكتورية
                    var vectorPage = CreateVectorPage(page, i + 1);
                    
                    // إنشاء PageContent
                    var pageContent = new PageContent();
                    ((IAddChild)pageContent).AddChild(vectorPage);
                    fixedDocument.Pages.Add(pageContent);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {pages.Count} صفحة فيكتورية بنجاح");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند الفيكتوري: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء صفحة فيكتورية واحدة
        /// </summary>
        private static FixedPage CreateVectorPage(FrameworkElement originalPage, int pageNumber)
        {
            try
            {
                // إنشاء صفحة A4 فيكتورية
                var fixedPage = new FixedPage
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White
                };

                // إنشاء Canvas للرسم الفيكتوري
                var canvas = new Canvas
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White
                };

                // تحويل المحتوى إلى فيكتور
                var vectorContent = ConvertToVector(originalPage);
                
                // إضافة المحتوى الفيكتوري
                canvas.Children.Add(vectorContent);

                // إضافة رقم الصفحة الفيكتوري
                var pageNumberElement = CreateVectorPageNumber(pageNumber);
                canvas.Children.Add(pageNumberElement);

                // إضافة Canvas إلى الصفحة
                fixedPage.Children.Add(canvas);

                return fixedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة الفيكتورية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحويل العنصر إلى فيكتور عالي الجودة
        /// </summary>
        private static FrameworkElement ConvertToVector(FrameworkElement original)
        {
            try
            {
                // إنشاء نسخة فيكتورية من العنصر الأصلي
                var xaml = XamlWriter.Save(original);
                var vectorElement = (FrameworkElement)XamlReader.Parse(xaml);

                // تطبيق خصائص الجودة الفيكتورية
                ApplyVectorQuality(vectorElement);

                // ضبط الحجم للصفحة A4
                vectorElement.Width = A4_WIDTH_DIP - (MARGIN_DIP * 2);
                vectorElement.Height = A4_HEIGHT_DIP - (MARGIN_DIP * 2);
                
                // وضع العنصر في المركز
                Canvas.SetLeft(vectorElement, MARGIN_DIP);
                Canvas.SetTop(vectorElement, MARGIN_DIP);

                // تحديث التخطيط
                vectorElement.Measure(new Size(vectorElement.Width, vectorElement.Height));
                vectorElement.Arrange(new Rect(0, 0, vectorElement.Width, vectorElement.Height));
                vectorElement.UpdateLayout();

                return vectorElement;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحويل العنصر إلى فيكتور: {ex.Message}");
                
                // في حالة الخطأ، إرجاع عنصر بديل
                return new TextBlock
                {
                    Text = "خطأ في تحويل المحتوى إلى فيكتور",
                    FontSize = 16,
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
            }
        }

        /// <summary>
        /// تطبيق خصائص الجودة الفيكتورية على العنصر
        /// </summary>
        private static void ApplyVectorQuality(DependencyObject element)
        {
            try
            {
                // تطبيق خصائص الجودة الفيكتورية
                if (element is FrameworkElement fe)
                {
                    fe.UseLayoutRounding = false; // إيقاف Layout Rounding للفيكتور
                    fe.SnapsToDevicePixels = false; // إيقاف Snap للفيكتور
                    
                    // تطبيق خصائص النص الفيكتوري
                    TextOptions.SetTextRenderingMode(fe, TextRenderingMode.ClearType);
                    TextOptions.SetTextFormattingMode(fe, TextFormattingMode.Ideal);
                    TextOptions.SetTextHintingMode(fe, TextHintingMode.Auto);
                }

                // تطبيق على العناصر الفرعية
                var childrenCount = VisualTreeHelper.GetChildrenCount(element);
                for (int i = 0; i < childrenCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(element, i);
                    ApplyVectorQuality(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ تحذير في تطبيق الجودة الفيكتورية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء رقم صفحة فيكتوري
        /// </summary>
        private static FrameworkElement CreateVectorPageNumber(int pageNumber)
        {
            try
            {
                // إنشاء Border فيكتوري لرقم الصفحة
                var pageNumberBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)),
                    BorderBrush = new SolidColorBrush(Color.FromArgb(100, 128, 128, 128)),
                    BorderThickness = new Thickness(1, 1, 1, 1),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(8, 4, 8, 4),
                    Width = 35,
                    Height = 20,
                    Child = new TextBlock
                    {
                        Text = pageNumber.ToString(),
                        FontSize = 11,
                        FontWeight = FontWeights.SemiBold,
                        Foreground = new SolidColorBrush(Color.FromRgb(64, 64, 64)),
                        TextAlignment = TextAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    }
                };

                // وضع رقم الصفحة في أسفل اليسار ومرفوع قليلاً
                Canvas.SetLeft(pageNumberBorder, 5);
                Canvas.SetBottom(pageNumberBorder, 25);

                // تطبيق خصائص الفيكتور
                ApplyVectorQuality(pageNumberBorder);

                return pageNumberBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء رقم الصفحة الفيكتوري: {ex.Message}");
                return new TextBlock { Text = pageNumber.ToString() };
            }
        }

        /// <summary>
        /// إنشاء DrawingVisual فيكتوري للطباعة المباشرة
        /// </summary>
        public static DrawingVisual CreateVectorDrawing(FrameworkElement element)
        {
            try
            {
                var drawingVisual = new DrawingVisual();
                
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // رسم خلفية بيضاء
                    drawingContext.DrawRectangle(Brushes.White, null, 
                        new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));

                    // إنشاء VisualBrush فيكتوري
                    var vectorBrush = new VisualBrush(element)
                    {
                        Stretch = Stretch.Fill,
                        TileMode = TileMode.None,
                        ViewboxUnits = BrushMappingMode.RelativeToBoundingBox,
                        ViewportUnits = BrushMappingMode.RelativeToBoundingBox,
                        AlignmentX = AlignmentX.Center,
                        AlignmentY = AlignmentY.Top
                    };

                    // رسم المحتوى الفيكتوري
                    var contentRect = new Rect(MARGIN_DIP, MARGIN_DIP, 
                        A4_WIDTH_DIP - (MARGIN_DIP * 2), 
                        A4_HEIGHT_DIP - (MARGIN_DIP * 2));
                    
                    drawingContext.DrawRectangle(vectorBrush, null, contentRect);
                }

                return drawingVisual;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الرسم الفيكتوري: {ex.Message}");
                throw;
            }
        }
    }
}
